import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import { SessionProvider } from "@/components/providers/session-provider";
import { Analytics } from "@vercel/analytics/next"

import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AIntegrity - AI Chat with Knowledge Base",
  description: "A ChatGPT wrapper with persistent knowledge base and note-taking capabilities",
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}
