import OpenAI from 'openai'
import { getTutoringSystemPrompt, detectAcademicIntegrityRisk } from './tutoring-prompts'
import { checkAcademicIntegrity, validateTutoringResponse, formatCitationReminder, generateSourceCitations } from './academic-integrity'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function generateEmbedding(text: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text,
  })
  
  return response.data[0].embedding
}

export async function generateChatCompletion(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  context?: string
) {
  // Get the last user message for academic integrity analysis
  const lastUserMessage = messages[messages.length - 1]?.content || '';

  // Analyze the user's request for academic integrity risks
  const integrityRisk = detectAcademicIntegrityRisk(lastUserMessage);

  // Use the comprehensive tutoring system prompt
  const systemMessage = {
    role: 'system' as const,
    content: getTutoringSystemPrompt(context)
  }

  // Generate the AI response
  const response = await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [systemMessage, ...messages],
    temperature: 0.7,
    max_tokens: 2000,
  })

  let aiResponse = response.choices[0]?.message?.content || '';

  // Validate the response for academic integrity compliance
  const integrityCheck = checkAcademicIntegrity(aiResponse);
  const responseValidation = validateTutoringResponse(aiResponse);

  // If the response violates academic integrity, use a safer alternative
  if (integrityCheck.isViolation) {
    console.warn('Academic integrity violation detected:', integrityCheck.explanation);
    aiResponse = integrityCheck.suggestedResponse ||
      "I'm here to help you learn! Instead of providing direct answers, let me guide you through understanding the concepts. What specific part would you like to explore together?";
  }

  // Log validation issues for monitoring (in development)
  if (!responseValidation.isValid && process.env.NODE_ENV === 'development') {
    console.log('Response validation issues:', responseValidation.issues);
    console.log('Suggestions:', responseValidation.suggestions);
  }

  // Log risk analysis for high-risk requests (in development)
  if (integrityRisk.riskLevel === 'high' && process.env.NODE_ENV === 'development') {
    console.log('High-risk academic request detected:', integrityRisk.triggers);
  }

  // Add source citations for educational content
  if (aiResponse.length > 100) {
    const sources = generateSourceCitations(lastUserMessage, 'general');
    const citationReminder = formatCitationReminder(sources);
    aiResponse += citationReminder;
  }

  return aiResponse;
}
